import Foundation
import SwiftUI
import UIKit

// MARK: - 动画偏好管理器
@Observable
@MainActor
final class AnimationPreferencesManager: @unchecked Sendable {
    static let shared = AnimationPreferencesManager()
    
    // MARK: - 动画强度设置
    enum AnimationIntensity: String, CaseIterable {
        case minimal = "minimal"
        case moderate = "moderate"
        case full = "full"
        case auto = "auto"
        
        var displayName: String {
            switch self {
            case .minimal: return "最少动画"
            case .moderate: return "适中动画"
            case .full: return "完整动画"
            case .auto: return "智能调节"
            }
        }
        
        var description: String {
            switch self {
            case .minimal: return "仅保留必要的状态反馈动画"
            case .moderate: return "平衡性能与视觉效果"
            case .full: return "完整的动画体验"
            case .auto: return "根据设备性能和使用习惯自动调节"
            }
        }
    }
    
    // MARK: - 学习行为分析数据
    struct LearningBehavior {
        var totalInteractions: Int = 0
        var averageSessionLength: TimeInterval = 0
        var preferredAnimationTypes: Set<String> = []
        var performanceIssues: Int = 0
        var lastAnalysisDate: Date = Date()
    }
    
    // MARK: - 设备性能评估
    struct DevicePerformance {
        let cpuCores: Int
        let ramSize: Int // GB
        let deviceModel: String
        let performanceScore: Double
        
        var recommendedIntensity: AnimationIntensity {
            switch performanceScore {
            case 0.0..<0.4: return .minimal
            case 0.4..<0.7: return .moderate
            default: return .full
            }
        }
    }
    
    // MARK: - 动画配置
    struct AnimationConfig {
        var duration: Double
        var dampingFraction: Double
        var particleCount: Int
        var enableParticles: Bool
        var enableTransitions: Bool
        var enableBackgroundEffects: Bool
        
        static let minimal = AnimationConfig(
            duration: 0.2,
            dampingFraction: 0.9,
            particleCount: 5,
            enableParticles: false,
            enableTransitions: true,
            enableBackgroundEffects: false
        )
        
        static let moderate = AnimationConfig(
            duration: 0.3,
            dampingFraction: 0.8,
            particleCount: 10,
            enableParticles: true,
            enableTransitions: true,
            enableBackgroundEffects: false
        )
        
        static let full = AnimationConfig(
            duration: 0.5,
            dampingFraction: 0.7,
            particleCount: 20,
            enableParticles: true,
            enableTransitions: true,
            enableBackgroundEffects: true
        )
    }
    
    // MARK: - 公开属性
    private(set) var currentIntensity: AnimationIntensity = .auto
    private(set) var currentConfig: AnimationConfig = .moderate
    private(set) var learningBehavior: LearningBehavior = LearningBehavior()
    private(set) var devicePerformance: DevicePerformance
    
    // 用户偏好
    var userPreferredIntensity: AnimationIntensity = .auto {
        didSet {
            updateAnimationConfig()
            savePreferences()
        }
    }
    
    var enableHapticFeedback: Bool = true {
        didSet { savePreferences() }
    }
    
    var enableSoundEffects: Bool = false {
        didSet { savePreferences() }
    }
    
    var enableSmartSuggestions: Bool = true {
        didSet { savePreferences() }
    }
    
    // MARK: - 私有属性
    private let userDefaults = UserDefaults.standard
    private var performanceMonitorTimer: Timer?
    private var behaviorAnalysisTimer: Timer?
    
    // UserDefaults Keys
    private let intensityKey = "AnimationPreferences.Intensity"
    private let hapticKey = "AnimationPreferences.Haptic"
    private let soundKey = "AnimationPreferences.Sound"
    private let suggestionsKey = "AnimationPreferences.SmartSuggestions"
    private let behaviorKey = "AnimationPreferences.LearningBehavior"
    
    // MARK: - 初始化
    private init() {
        self.devicePerformance = Self.evaluateDevicePerformance()
        loadPreferences()
        updateAnimationConfig()
        startPerformanceMonitoring()
        startBehaviorAnalysis()
    }
    
    deinit {
        Task { @MainActor in
            performanceMonitorTimer?.invalidate()
            behaviorAnalysisTimer?.invalidate()
        }
    }
    
    // MARK: - 设备性能评估
    private static func evaluateDevicePerformance() -> DevicePerformance {
        let processInfo = ProcessInfo.processInfo
        let physicalMemory = processInfo.physicalMemory
        let ramGB = Int(physicalMemory / (1024 * 1024 * 1024))
        
        // 获取设备型号
        var systemInfo = utsname()
        uname(&systemInfo)
        let deviceModel = withUnsafePointer(to: &systemInfo.machine) {
            $0.withMemoryRebound(to: CChar.self, capacity: 1) {
                String(validatingUTF8: $0) ?? "Unknown"
            }
        }
        
        // 简化的性能评分（实际项目中可以更精确）
        let cpuCores = processInfo.processorCount
        var performanceScore = 0.0
        
        // 基于内存和处理器核心数计算性能分数
        performanceScore += Double(ramGB) / 8.0 * 0.4  // 内存权重40%
        performanceScore += Double(cpuCores) / 8.0 * 0.3  // CPU权重30%
        
        // 基于设备型号调整（简化版本）
        if deviceModel.contains("iPhone15") || deviceModel.contains("iPhone16") {
            performanceScore += 0.3  // 新设备加分30%
        } else if deviceModel.contains("iPhone14") || deviceModel.contains("iPhone13") {
            performanceScore += 0.2  // 中等设备加分20%
        } else if deviceModel.contains("iPhone12") || deviceModel.contains("iPhone11") {
            performanceScore += 0.1  // 较老设备加分10%
        }
        
        // 限制在0-1范围内
        performanceScore = min(1.0, max(0.0, performanceScore))
        
        return DevicePerformance(
            cpuCores: cpuCores,
            ramSize: ramGB,
            deviceModel: deviceModel,
            performanceScore: performanceScore
        )
    }
    
    // MARK: - 偏好存储
    private func loadPreferences() {
        if let intensityRaw = userDefaults.string(forKey: intensityKey),
           let intensity = AnimationIntensity(rawValue: intensityRaw) {
            userPreferredIntensity = intensity
        }
        
        enableHapticFeedback = userDefaults.bool(forKey: hapticKey)
        enableSoundEffects = userDefaults.bool(forKey: soundKey)
        enableSmartSuggestions = userDefaults.bool(forKey: suggestionsKey)
        
        // 加载学习行为数据
        if let behaviorData = userDefaults.data(forKey: behaviorKey),
           let behavior = try? JSONDecoder().decode(LearningBehavior.self, from: behaviorData) {
            learningBehavior = behavior
        }
        
        // 首次使用默认设置
        if !userDefaults.bool(forKey: "AnimationPreferences.FirstLaunch") {
            enableHapticFeedback = true
            enableSoundEffects = false
            enableSmartSuggestions = true
            userDefaults.set(true, forKey: "AnimationPreferences.FirstLaunch")
            savePreferences()
        }
    }
    
    private func savePreferences() {
        userDefaults.set(userPreferredIntensity.rawValue, forKey: intensityKey)
        userDefaults.set(enableHapticFeedback, forKey: hapticKey)
        userDefaults.set(enableSoundEffects, forKey: soundKey)
        userDefaults.set(enableSmartSuggestions, forKey: suggestionsKey)
        
        // 保存学习行为数据
        if let behaviorData = try? JSONEncoder().encode(learningBehavior) {
            userDefaults.set(behaviorData, forKey: behaviorKey)
        }
    }
    
    // MARK: - 动画配置更新
    private func updateAnimationConfig() {
        let targetIntensity: AnimationIntensity
        
        if userPreferredIntensity == .auto {
            // 智能调节模式
            targetIntensity = determineOptimalIntensity()
        } else {
            // 用户手动设置
            targetIntensity = userPreferredIntensity
        }
        
        currentIntensity = targetIntensity
        
        switch targetIntensity {
        case .minimal:
            currentConfig = .minimal
        case .moderate:
            currentConfig = .moderate
        case .full, .auto:
            currentConfig = .full
        }
    }
    
    private func determineOptimalIntensity() -> AnimationIntensity {
        var score = devicePerformance.performanceScore
        
        // 根据学习行为调整
        if learningBehavior.performanceIssues > 3 {
            score -= 0.2  // 有性能问题，降低动画强度
        }
        
        if learningBehavior.averageSessionLength > 300 { // 5分钟以上
            score += 0.1  // 长时间学习，可能喜欢丰富体验
        }
        
        // 基于系统减弱动画设置
        if UIAccessibility.isReduceMotionEnabled {
            return .minimal
        }
        
        // 基于低电量模式
        if ProcessInfo.processInfo.isLowPowerModeEnabled {
            return .minimal
        }
        
        switch score {
        case 0.0..<0.4: return .minimal
        case 0.4..<0.7: return .moderate
        default: return .full
        }
    }
    
    // MARK: - 性能监控
    private func startPerformanceMonitoring() {
        performanceMonitorTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.checkPerformanceIssues()
            }
        }
    }
    
    private func checkPerformanceIssues() {
        // 检查内存使用情况
        var memoryInfo = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let result = withUnsafeMutablePointer(to: &memoryInfo) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        if result == KERN_SUCCESS {
            let usedMemoryMB = Double(memoryInfo.resident_size) / (1024 * 1024)
            
            // 如果内存使用超过阈值，记录性能问题
            if usedMemoryMB > Double(devicePerformance.ramSize) * 0.8 * 1024 {
                learningBehavior.performanceIssues += 1
                
                // 如果启用智能建议且性能问题较多，自动降低动画强度
                if enableSmartSuggestions && learningBehavior.performanceIssues >= 3 {
                    if userPreferredIntensity == .auto && currentIntensity != .minimal {
                        updateAnimationConfig()
                    }
                }
            }
        }
    }
    
    // MARK: - 学习行为分析
    private func startBehaviorAnalysis() {
        behaviorAnalysisTimer = Timer.scheduledTimer(withTimeInterval: 300.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                self?.analyzeLearningBehavior()
            }
        }
    }
    
    private func analyzeLearningBehavior() {
        // 更新分析时间
        learningBehavior.lastAnalysisDate = Date()
        savePreferences()
    }
    
    // MARK: - 公开接口
    
    /// 记录用户交互
    func recordInteraction() {
        learningBehavior.totalInteractions += 1
    }
    
    /// 记录动画偏好
    func recordAnimationPreference(_ animationType: String) {
        learningBehavior.preferredAnimationTypes.insert(animationType)
    }
    
    /// 获取当前动画配置
    func getAnimationConfig() -> AnimationConfig {
        return currentConfig
    }
    
    /// 获取Spring动画参数
    func getSpringAnimation(duration: Double? = nil) -> Animation {
        let actualDuration = duration ?? currentConfig.duration
        return .spring(
            response: actualDuration,
            dampingFraction: currentConfig.dampingFraction
        )
    }
    
    /// 获取简单动画参数
    func getEaseAnimation(duration: Double? = nil) -> Animation {
        let actualDuration = duration ?? currentConfig.duration
        return .easeInOut(duration: actualDuration)
    }
    
    /// 是否应该显示粒子效果
    func shouldShowParticles() -> Bool {
        return currentConfig.enableParticles
    }
    
    /// 是否应该显示转场动画
    func shouldShowTransitions() -> Bool {
        return currentConfig.enableTransitions
    }
    
    /// 获取统一的过渡动画
    func getTransition(type: TransitionType = .standard) -> AnyTransition {
        guard shouldShowTransitions() else { return .identity }

        switch type {
        case .standard:
            return .opacity.animation(getEaseAnimation(duration: 0.3))
        case .scale:
            return .scale.combined(with: .opacity).animation(getSpringAnimation(duration: 0.4))
        case .slide(let edge):
            return .move(edge: edge).combined(with: .opacity).animation(getSpringAnimation(duration: 0.4))
        case .asymmetricScale:
            return .asymmetric(
                insertion: .scale(scale: 0.9).combined(with: .opacity).animation(getSpringAnimation(duration: 0.4)),
                removal: .opacity.animation(getEaseAnimation(duration: 0.2))
            )
        }
    }

    /// 过渡动画类型
    enum TransitionType {
        case standard           // 简单的透明度变化
        case scale             // 缩放 + 透明度
        case slide(Edge)       // 滑动 + 透明度
        case asymmetricScale   // 非对称缩放
    }

    /// 是否应该显示背景效果
    func shouldShowBackgroundEffects() -> Bool {
        return currentConfig.enableBackgroundEffects
    }
    
    /// 获取粒子数量
    func getParticleCount() -> Int {
        return currentConfig.particleCount
    }
    
    /// 强制更新配置（用于设置界面）
    func forceUpdateConfig() {
        updateAnimationConfig()
    }
    
    /// 重置学习行为数据
    func resetLearningBehavior() {
        learningBehavior = LearningBehavior()
        savePreferences()
    }
    
    /// 获取性能建议
    func getPerformanceSuggestion() -> String? {
        if learningBehavior.performanceIssues >= 3 {
            return "检测到性能问题，建议降低动画强度以获得更流畅的体验"
        }
        
        if devicePerformance.performanceScore < 0.4 && currentIntensity == .full {
            return "您的设备性能较低，建议使用适中或最少动画模式"
        }
        
        return nil
    }
    
    /// 获取设备信息（用于调试）
    func getDeviceInfo() -> String {
        return """
        设备型号: \(devicePerformance.deviceModel)
        CPU核心: \(devicePerformance.cpuCores)
        内存: \(devicePerformance.ramSize)GB
        性能评分: \(String(format: "%.2f", devicePerformance.performanceScore))
        推荐强度: \(devicePerformance.recommendedIntensity.displayName)
        当前强度: \(currentIntensity.displayName)
        """
    }
}

// MARK: - View 扩展
extension View {
    /// 应用智能过渡动画
    func smartTransition(_ type: AnimationPreferencesManager.TransitionType = .standard) -> some View {
        let animationManager = AnimationPreferencesManager.shared
        return self.transition(animationManager.getTransition(type: type))
    }
}

// MARK: - LearningBehavior Codable 支持
extension AnimationPreferencesManager.LearningBehavior: Codable {
    enum CodingKeys: String, CodingKey {
        case totalInteractions
        case averageSessionLength
        case preferredAnimationTypes
        case performanceIssues
        case lastAnalysisDate
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        totalInteractions = try container.decode(Int.self, forKey: .totalInteractions)
        averageSessionLength = try container.decode(TimeInterval.self, forKey: .averageSessionLength)
        let types = try container.decode([String].self, forKey: .preferredAnimationTypes)
        preferredAnimationTypes = Set(types)
        performanceIssues = try container.decode(Int.self, forKey: .performanceIssues)
        lastAnalysisDate = try container.decode(Date.self, forKey: .lastAnalysisDate)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(totalInteractions, forKey: .totalInteractions)
        try container.encode(averageSessionLength, forKey: .averageSessionLength)
        try container.encode(Array(preferredAnimationTypes), forKey: .preferredAnimationTypes)
        try container.encode(performanceIssues, forKey: .performanceIssues)
        try container.encode(lastAnalysisDate, forKey: .lastAnalysisDate)
    }
} 