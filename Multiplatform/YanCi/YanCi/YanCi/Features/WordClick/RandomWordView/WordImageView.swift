import SwiftUI
import UIKit // for UIImage

// MARK: - 单词图片显示组件
struct WordImageView: View {
    let word: String
    let wordDetailManager: WordDetailManager
    
    // 图片比例 1408:768 ≈ 1.83:1
    private let imageAspectRatio: CGFloat = 1408.0 / 768.0
    
    var body: some View {
        GeometryReader { geometry in
            let maxWidth = geometry.size.width
            let imageHeight = maxWidth / imageAspectRatio
            
            ZStack {
                // 图片层
                Group {
                    // 尝试从wordpic.xcassets或Assets.xcassets中加载图片
                    if let image = loadWordImage() {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(imageAspectRatio, contentMode: .fit)
                            .frame(width: maxWidth, height: imageHeight)
                            .clipShape(RoundedRectangle(cornerRadius: 16))
                            .overlay(
                                // 添加内阴影效果
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(
                                        LinearGradient(
                                            colors: [.white.opacity(0.3), .clear],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 1
                                    )
                            )
                            .shadow(color: .black.opacity(0.15), radius: 12, x: 0, y: 6)
                            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                            .background(
                                // 背景渐变增加质感
                                RoundedRectangle(cornerRadius: 16)
                                    .fill(
                                        LinearGradient(
                                            colors: [.white.opacity(0.1), .clear],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                            )
                    } else {
                        // 默认占位图 - 保持相同比例
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    colors: [
                                        Color.animalBorder.opacity(0.3),
                                        Color.animalBorder.opacity(0.5)
                                    ],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: maxWidth, height: imageHeight)
                            .overlay(
                                VStack(spacing: 8) {
                                    Image(systemName: "photo.artframe")
                                        .font(.system(size: 32, weight: .light))
                                        .foregroundColor(Color.animalSecondaryText.opacity(0.6))
                                    Text("无图片")
                                        .font(.subheadline)
                                        .fontWeight(.bold)
                                        .fontDesign(.rounded)
                                        .foregroundColor(Color.animalSecondaryText.opacity(0.7))
                                }
                            )
                            .overlay(
                                // 占位图边框
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(
                                        LinearGradient(
                                            colors: [.white.opacity(0.2), .clear],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        ),
                                        lineWidth: 1
                                    )
                            )
                            .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
                    }
                }
                
                // 例句动画覆盖层
                if wordDetailManager.showQuoteAnimation,
                   let quote = wordDetailManager.currentQuote {
                    QuoteAnimationOverlay(
                        quote: quote,
                        onTapped: {
                            wordDetailManager.presentDetailSheet()
                        }
                    )
                    .frame(width: maxWidth, height: imageHeight)
                    .clipShape(RoundedRectangle(cornerRadius: 16))
                }
                
            }
        }
        .frame(height: UIScreen.main.bounds.width * 0.9 / imageAspectRatio)
        .padding(.horizontal, 5)
        .sheet(isPresented: Binding(
            get: { wordDetailManager.showDetailSheet },
            set: { newValue in
                if !newValue {
                    wordDetailManager.dismissDetailSheet()
                    wordDetailManager.hideQuoteAnimation()
                }
            }
        )) {
            if let wordDetail = wordDetailManager.currentWordDetail {
                WordDetailSheet(wordDetail: wordDetail)
            }
        }
    }
    
    private func loadWordImage() -> UIImage? {
        // 尝试多种可能的图片名称格式
        let possibleNames = [
            word,                    // 直接使用单词名
            "\(word).jpg",          // 单词名 + .jpg
            word.lowercased(),      // 小写单词名
            "\(word.lowercased()).jpg", // 小写单词名 + .jpg
        ]
        
        for imageName in possibleNames {
            if let image = UIImage(named: imageName) {
                return image
            }
        }
        
        return nil
    }
} 
