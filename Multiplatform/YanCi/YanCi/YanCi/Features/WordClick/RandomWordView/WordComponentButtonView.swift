import SwiftUI

// MARK: - 晃动动画效果
struct ShakeEffect: GeometryEffect {
    var amount: CGFloat = 10
    var shakesPerUnit = 3
    var animatableData: CGFloat

    func effectValue(size: CGSize) -> ProjectionTransform {
        ProjectionTransform(CGAffineTransform(translationX:
            amount * sin(animatableData * .pi * CGFloat(shakesPerUnit)),
            y: 0))
    }
}



// MARK: - 单词组件按钮视图
struct WordComponentButtonView: View {
    let componentButton: WordComponentButton
    let onTap: () -> Void
    
    @State private var shakeAmount: CGFloat = 0
    @State private var isPressed = false
    @State private var showHint = false
    
    var body: some View {
        Button(action: {
            withAnimation(.easeInOut(duration: 0.1)) {
                isPressed = true
            }
            
            // 延迟恢复按钮状态
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = false
                }
            }
            
            onTap()
        }) {
            Text(componentButton.component.displayText)
                .font(.title)
                .fontWeight(.bold)
                .fontDesign(.rounded)
                .frame(minWidth: 44, minHeight: 50)
                .padding(.horizontal, 6)
                .background(buttonBackground)
                .foregroundColor(buttonForegroundColor)
                .clipShape(RoundedRectangle(cornerRadius: 10))
                .overlay(
                    RoundedRectangle(cornerRadius: 10)
                        .stroke(strokeColor, lineWidth: strokeWidth)
                )
                .shadow(color: shadowColor, radius: shadowRadius, x: 0, y: shadowY)
                .scaleEffect(buttonScale)
                .modifier(ShakeEffect(animatableData: shakeAmount))
        }
        .disabled(componentButton.isClicked)
        .animation(.easeInOut(duration: 0.2), value: componentButton.isClicked)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: componentButton.isCorrect)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
        .onChange(of: componentButton.isCorrect) { oldValue, newValue in
            if let isCorrect = newValue {
                if !isCorrect {
                    // 错误时触发晃动动画
                    triggerShakeAnimation()
                }
            }
        }

    }
    
    // MARK: - 动画触发
    private func triggerShakeAnimation() {
        withAnimation(.linear(duration: 0.1).repeatCount(3, autoreverses: true)) {
            shakeAmount = 1.0
        }
        
        // 动画结束后重置
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            shakeAmount = 0
        }
    }
    
    // MARK: - 计算属性
    private var buttonScale: CGFloat {
        if isPressed {
            return 0.95
        } else if componentButton.isClicked {
            return 0.9
        } else {
            return 1.0
        }
    }
    
    private var shadowRadius: CGFloat {
        if componentButton.isClicked || isPressed {
            return 2
        } else {
            return 4
        }
    }
    
    private var shadowY: CGFloat {
        if componentButton.isClicked || isPressed {
            return 1
        } else {
            return 2
        }
    }
    


    private var buttonBackground: some ShapeStyle {
        if componentButton.isClicked {
            if let isCorrect = componentButton.isCorrect {
                return AnyShapeStyle(isCorrect ? Color.animalSuccess.opacity(0.3) : Color.animalError.opacity(0.3))
            }
            return AnyShapeStyle(Color.animalBorder.opacity(0.3))
        }
        
        switch componentButton.component {
        case .letter:
            return AnyShapeStyle(Color.animalButton.gradient)
        case .morpheme(_, let type):
            switch type {
            case .prefix:
                return AnyShapeStyle(Color.animalPrefix.gradient)
            case .root:
                return AnyShapeStyle(Color.animalRoot.gradient)
            case .suffix:
                return AnyShapeStyle(Color.animalSuffix.gradient)
            }
        case .syllable:
            return AnyShapeStyle(Color.animalAccent.gradient)
        }
    }
    
    private var buttonForegroundColor: Color {
        componentButton.isClicked ? Color.animalSecondaryText : .white
    }
    
    private var strokeColor: Color {
        if let isCorrect = componentButton.isCorrect {
            return isCorrect ? Color.animalSuccess : Color.animalError
        }
        return .clear
    }
    
    private var strokeWidth: CGFloat {
        componentButton.isCorrect != nil ? 2 : 0
    }
    
    private var shadowColor: Color {
        if componentButton.isClicked || isPressed {
            return .clear
        }
        
        switch componentButton.component {
        case .letter:
            return Color.animalButton.opacity(0.3)
        case .morpheme(_, let type):
            switch type {
            case .prefix:
                return Color.animalPrefix.opacity(0.3)
            case .root:
                return Color.animalRoot.opacity(0.3)
            case .suffix:
                return Color.animalSuffix.opacity(0.3)
            }
        case .syllable:
            return Color.animalAccent.opacity(0.3)
        }
    }
}

// MARK: - 辅助视图修饰符
// 已移动到 SharedViewModifiers.swift

// MARK: - 流式布局
struct FlowLayout: Layout {
    var spacing: CGFloat = 8
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let result = FlowResult(
            in: proposal.replacingUnspecifiedDimensions(),
            subviews: subviews,
            spacing: spacing
        )
        return result.bounds
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let result = FlowResult(
            in: bounds.size,
            subviews: subviews,
            spacing: spacing
        )
        for (index, subview) in subviews.enumerated() {
            let frame = result.frames[index]
            let position = CGPoint(
                x: frame.origin.x + bounds.origin.x,
                y: frame.origin.y + bounds.origin.y
            )
            subview.place(at: position, proposal: .unspecified)
        }
    }
    
    struct FlowResult {
        var bounds = CGSize.zero
        var frames: [CGRect] = []
        
        init(in bounds: CGSize, subviews: LayoutSubviews, spacing: CGFloat) {
            var origin = CGPoint.zero
            var lineHeight: CGFloat = 0
            var lineFrames: [CGRect] = []
            
            for subview in subviews {
                let size = subview.sizeThatFits(.unspecified)
                
                if origin.x + size.width > bounds.width && !lineFrames.isEmpty {
                    // 换行
                    origin.x = 0
                    origin.y += lineHeight + spacing
                    lineHeight = 0
                }
                
                let frame = CGRect(origin: origin, size: size)
                lineFrames.append(frame)
                frames.append(frame)
                
                origin.x += size.width + spacing
                lineHeight = max(lineHeight, size.height)
            }
            
            if !frames.isEmpty {
                let maxX = frames.map { $0.maxX }.max() ?? 0
                let maxY = frames.map { $0.maxY }.max() ?? 0
                self.bounds = CGSize(width: maxX, height: maxY)
            }
        }
    }
}

// MARK: - 词根信息面板
struct MorphemeInfoPanel: View {
    let components: [WordComponent]
    
    var body: some View {
        let morphemes = components.compactMap { component -> (String, MorphemeType)? in
            if case .morpheme(let text, let type) = component {
                return (text, type)
            }
            return nil
        }
        
        let syllables = components.compactMap { component -> String? in
            if case .syllable(let text) = component {
                return text
            }
            return nil
        }
        
        let hasInfo = !morphemes.isEmpty || !syllables.isEmpty
        
        if hasInfo {
            VStack(spacing: 12) {

                // 标题
                HStack {
                    Image(systemName: "info.circle.fill")
                        .foregroundColor(Color.animalAccent)
                    Text(getInfoTitle())
                        .font(.subheadline)
                        .fontWeight(.semibold)
                    
                    Spacer()
                    
                }
                
                // 信息内容
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        // 词根信息
                        ForEach(morphemes, id: \.0) { text, type in
                            HStack(spacing: 4) {
                                Text(text)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 3)
                                    .background(backgroundForType(type))
                                    .foregroundColor(.white)
                                    .clipShape(RoundedRectangle(cornerRadius: 4))
                                
                                Text(getMorphemeDescription(text, type))
                                    .font(.caption2)
                                    .foregroundColor(Color.animalSecondaryText)
                            }
                        }
                        
                        // 音节信息
                        ForEach(syllables, id: \.self) { syllable in
                            HStack(spacing: 4) {
                                Text(syllable)
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 3)
                                    .background(Color.animalAccent)
                                    .foregroundColor(.white)
                                    .clipShape(RoundedRectangle(cornerRadius: 4))
                                
                                Text("音节")
                                    .font(.caption2)
                                    .foregroundColor(Color.animalSecondaryText)
                            }
                        }
                    }
                    .padding(.horizontal, 4)
                }
            }
            .padding(12)
            .background(Color.animalCardBackground.opacity(0.9), in: .rect(cornerRadius: 10))
            .shadow(color: Color.animalBorder.opacity(0.2), radius: 4, x: 0, y: 2)
        }
        
    }
    
    private func getInfoTitle() -> String {
        let morphemes = components.compactMap { component -> (String, MorphemeType)? in
            if case .morpheme(let text, let type) = component {
                return (text, type)
            }
            return nil
        }
        
        let syllables = components.compactMap { component -> String? in
            if case .syllable(let text) = component {
                return text
            }
            return nil
        }
        
        if !morphemes.isEmpty && !syllables.isEmpty {
            return "词根和音节提示"
        } else if !morphemes.isEmpty {
            return "词根提示"
        } else {
            return "音节提示"
        }
    }
    
    private func backgroundForType(_ type: MorphemeType) -> some ShapeStyle {
        switch type {
        case .prefix: return AnyShapeStyle(Color.animalPrefix)
        case .root: return AnyShapeStyle(Color.animalRoot)
        case .suffix: return AnyShapeStyle(Color.animalSuffix)
        }
    }
    
    private func getMorphemeDescription(_ text: String, _ type: MorphemeType) -> String {
        switch type {
        case .prefix:
            return WordMorphologyService.commonPrefixes[text] ?? "前缀"
        case .root:
            return WordMorphologyService.commonRoots[text] ?? "词根"
        case .suffix:
            return WordMorphologyService.commonSuffixes[text] ?? "后缀"
        }
    }
} 
