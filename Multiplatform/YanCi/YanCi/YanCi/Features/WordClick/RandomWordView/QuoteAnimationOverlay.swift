import SwiftUI

// MARK: - 例句动画覆盖层
struct QuoteAnimationOverlay: View {
    let quote: Quote
    let onTapped: () -> Void

    @State private var englishCharacters: [String] = []
    @State private var chineseCharacters: [String] = []
    @State private var visibleEnglishCount: Int = 0
    @State private var visibleChineseCount: Int = 0
    @State private var showChinese: Bool = false
    @State private var animationPhase: AnimationPhase = .waiting

    // 动画配置
    private let characterDelay: Double = 0.05
    private let phaseDelay: Double = 1.0

    enum AnimationPhase {
        case waiting
        case showingEnglish
        case showingChinese
        case complete
    }

    var body: some View {
        VStack(spacing: 16) {
            Spacer()

            // 例句容器
            VStack(alignment: .leading, spacing: 12) {
                // 英文例句
                if animationPhase == .showingEnglish || animationPhase == .showingChinese || animationPhase == .complete {
                    englishSentenceView
                }

                // 中文例句
                if animationPhase == .showingChinese || animationPhase == .complete {
                    chineseSentenceView
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 16)
                    .fill(.ultraThinMaterial)
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.white.opacity(0.4), lineWidth: 0)
                    )
            )
            .shadow(color: .black.opacity(0.2), radius: 8, x: 0, y: 4)
            .padding(.horizontal, 20)
            .padding(.bottom, 20)
            .onTapGesture {
                onTapped()
            }
        }
        .onAppear {
            startAnimation()
        }
    }

    // MARK: - 英文句子视图
    private var englishSentenceView: some View {
        Text(englishCharacters.prefix(visibleEnglishCount).joined())
            .font(.body)
            .fontWeight(.medium)
            .fontDesign(.rounded)
            .foregroundColor(Color.animalTextColor)
            .multilineTextAlignment(.leading)
            .lineLimit(nil)
            .frame(maxWidth: .infinity, alignment: .leading)
            .animation(
                .spring(response: 0.4, dampingFraction: 0.8),
                value: visibleEnglishCount
            )
    }

    // MARK: - 中文句子视图
    private var chineseSentenceView: some View {
        Text(chineseCharacters.prefix(visibleChineseCount).joined())
            .font(.subheadline)
            .fontWeight(.medium)
            .fontDesign(.rounded)
            .foregroundColor(Color.animalTextColor)
            .opacity(0.5)
            .multilineTextAlignment(.leading)
            .lineLimit(nil)
            .frame(maxWidth: .infinity, alignment: .leading)
            .animation(
                .spring(response: 0.4, dampingFraction: 0.8),
                value: visibleChineseCount
            )
    }

    // MARK: - 动画控制
    private func startAnimation() {
        // 准备字符数组
        englishCharacters = Array(quote.english).map { String($0) }
        chineseCharacters = Array(quote.chinese).map { String($0) }

        // 开始动画序列
        withAnimation(.easeInOut(duration: 0.3)) {
            animationPhase = .showingEnglish
        }

        // 逐字符显示英文
        Task { @MainActor in
            for i in 0...englishCharacters.count {
                visibleEnglishCount = i
                try? await Task.sleep(nanoseconds: UInt64(characterDelay * 1_000_000_000))
            }

            // 等待一段时间后显示中文
            try? await Task.sleep(nanoseconds: UInt64(phaseDelay * 1_000_000_000))

            withAnimation(.easeInOut(duration: 0.3)) {
                animationPhase = .showingChinese
            }

            // 逐字符显示中文
            for i in 0...chineseCharacters.count {
                visibleChineseCount = i
                try? await Task.sleep(nanoseconds: UInt64(characterDelay * 1_000_000_000))
            }

            animationPhase = .complete
        }
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Rectangle()
            .fill(.blue.gradient)
            .ignoresSafeArea()

        QuoteAnimationOverlay(
            quote: Quote(
                english: "This is a wonderful example sentence.",
                chinese: "这是一个很棒的例句。",
                source: nil
            ),
            onTapped: {
                print("Quote tapped!")
            }
        )
    }
}
