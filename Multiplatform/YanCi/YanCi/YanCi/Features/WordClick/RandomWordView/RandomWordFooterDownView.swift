import SwiftUI



// MARK: - 体力值进度条动画
struct EnergyProgressBar: View {
    let current: Int
    let maximum: Int
    @State private var animatedProgress: Double = 0
    
    private var progress: Double {
        guard maximum > 0 else { return 0 }
        return Double(current) / Double(maximum)
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景
                Rectangle()
                    .fill(Color.animalSecondaryText.opacity(0.2))
                    .frame(height: 4)
                    .cornerRadius(2)
                
                // 进度条
                Rectangle()
                    .fill(progressGradient)
                    .frame(
                        width: max(0, min(geometry.size.width * animatedProgress, geometry.size.width)),
                        height: 4
                    )
                    .cornerRadius(2)
                    .overlay(
                        // 闪光效果
                        Rectangle()
                            .fill(
                                LinearGradient(
                                    colors: [.clear, .white.opacity(0.3), .clear],
                                    startPoint: .leading,
                                    endPoint: .trailing
                                )
                            )
                            .frame(width: 20)
                    )
            }
        }
        .frame(height: 4)
        .onAppear {
            withAnimation(.easeOut(duration: 0.8)) {
                animatedProgress = progress
            }
        }
        .onChange(of: progress) { _, newValue in
            withAnimation(.spring(response: 0.6, dampingFraction: 0.8)) {
                animatedProgress = newValue
            }
        }
    }
    
    private var progressGradient: LinearGradient {
        let colors: [Color] = progress > 0.5 ? 
            [.green, .yellow] : 
            progress > 0.2 ? [.yellow, .orange] : [.orange, .red]
        
        return LinearGradient(
            colors: colors,
            startPoint: .leading,
            endPoint: .trailing
        )
    }
}



// MARK: - 按钮按下动画
struct ButtonPressAnimation: ViewModifier {
    @State private var isPressed = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .opacity(isPressed ? 0.8 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .onTapGesture {
                // 触发按压动画
                withAnimation(.easeInOut(duration: 0.1)) {
                    isPressed = true
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = false
                    }
                }
            }
    }
}

// MARK: - 分类选择按钮增强
struct CategoryButtonEnhanced: View {
    let title: String
    let isPathMode: Bool
    let progress: String?
    let onTap: () -> Void
    let onLongPress: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 4) {
                HStack(spacing: 6) {
                    Image(systemName: isPathMode ? "map.fill" : "list.bullet")
                        .font(.caption)
                        .foregroundColor(Color.animalAccent)
                    
                    Text(title)
                        .font(.caption)
                        .fontWeight(.medium)
                        .fontDesign(.rounded)
                        .foregroundColor(Color.animalTextColor)
                        .lineLimit(1)
                }
                
                // 进度显示 - 始终显示进度信息（如果有）
                if let progress = progress {
                    Text(progress)
                        .font(.caption2)
                        .fontWeight(.medium)
                        .foregroundColor(Color.animalSecondaryText)
                        .lineLimit(1)
                        .animation(.easeInOut(duration: 0.3), value: progress)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(.regularMaterial, in: .rect(cornerRadius: 10))
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .shadow(color: .black.opacity(isPressed ? 0.1 : 0.2), radius: isPressed ? 2 : 4, x: 0, y: isPressed ? 1 : 2)
        }
        .buttonStyle(.plain)
        .onLongPressGesture(minimumDuration: 0.5) {
            // 保留长按功能用于其他用途
            onLongPress()
        }
        .animation(.easeInOut(duration: 0.2), value: isPressed)
        .animation(.easeInOut(duration: 0.3), value: progress)
    }
}

struct RandomWordFooterDownView: View {
    let viewModel: RandomWordViewModel
    @Binding var showStreakView: Bool
    @Binding var showCategorySelection: Bool
    @Binding var showPathProgress: Bool
    @Binding var showCategoryProgress: Bool

    var body: some View {
        HStack {
            energySection
            Spacer()
//            streakSection  
            Spacer()
            controlsSection
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(.regularMaterial, in: .rect(cornerRadius: 12))
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.animalBorder.opacity(0.2), lineWidth: 1)
        )
    }
    
    // MARK: - 子视图
    
    private var energySection: some View {
        let energyInfo = viewModel.getEnergyInfo()
        
        return Button(action: {
            Task { @MainActor in
                viewModel.restoreEnergyForTesting()
            }
        }) {
            VStack(spacing: 4) {
                HStack(spacing: 6) {
                    Image(systemName: "heart.fill")
                        .foregroundColor(.red)
                        .font(.caption)


                    Text("体力: \(energyInfo.current)")
                        .font(.caption)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .foregroundColor(Color.animalTextColor)
                }
                
                EnergyProgressBar(
                    current: energyInfo.current,
                    maximum: energyInfo.max
                )
                .frame(width: 60)
            }
        }
        .buttonStyle(.plain)
    }
    
    private var streakSection: some View {
        Button(action: {
            showStreakView = true
        }) {
            HStack(spacing: 6) {
                Image(systemName: viewModel.dailyStreakManager.hasLoggedInToday ? "checkmark.circle.fill" : "calendar")
                    .foregroundColor(viewModel.dailyStreakManager.hasLoggedInToday ? .green : Color.animalAccent)
                    .font(.caption)


                VStack(spacing: 2) {
                    Text("签到")
                        .font(.caption2)
                        .fontWeight(.medium)
                        .fontDesign(.rounded)
                        .foregroundColor(Color.animalTextColor)
                    
                    Text("\(viewModel.dailyStreakManager.currentStreak)天")
                        .font(.caption2)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .foregroundColor(Color.animalAccent)
                }
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 6)
            .background(.regularMaterial, in: .rect(cornerRadius: 8))
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(viewModel.dailyStreakManager.hasLoggedInToday ? .green.opacity(0.5) : .clear, lineWidth: 1)
            )
        }
        .buttonStyle(.plain)
    }
    
    private var controlsSection: some View {
        HStack(spacing: 8) {
            // 仅在路径学习模式下显示路径按钮
            if viewModel.selectedCategory == .path {
                Button(action: {
                    showPathProgress = true
                }) {
                    Image(systemName: "point.bottomleft.forward.to.arrow.triangle.uturn.scurvepath")
                        .font(.caption)
                        .foregroundColor(Color.animalAccent)
                        .frame(width: 24, height: 24)
                        .background(.regularMaterial, in: .circle)
                        .overlay(
                            Circle()
                                .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
                        )
                }
                .buttonStyle(.plain)
            }
            
            // 其他分类显示分类进度按钮
            if viewModel.selectedCategory != .path && viewModel.selectedCategory != .starred && viewModel.selectedCategory != .wrongWords {
                Button(action: {
                    showCategoryProgress = true
                }) {
                    Image(systemName: "chart.bar.fill")
                        .font(.caption)
                        .foregroundColor(Color.animalAccent)
                        .frame(width: 24, height: 24)
                        .background(.regularMaterial, in: .circle)
                        .overlay(
                            Circle()
                                .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
                        )
                }
                .buttonStyle(.plain)
            }
            
            // 分类选择按钮
            CategoryButtonEnhanced(
                title: viewModel.selectedCategory.displayName,
                isPathMode: viewModel.selectedCategory == .path,
                progress: getProgressText(),
                onTap: {
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                        showCategorySelection = true
                    }
                },
                onLongPress: {
                    // 移除长按功能，统一使用独立按钮
                }
            )
        }
    }
    
    // MARK: - 私有方法
    
    private func getProgressText() -> String? {
        switch viewModel.selectedCategory {
        case .path:
            // 直接访问ViewModel的可观测属性而不是调用方法
            let progressInfo = viewModel.pathProgressInfo
            return "\(progressInfo.currentGroup)/\(progressInfo.totalGroups)"
        case .starred:
            return viewModel.starredWordsCount > 0 ? "\(viewModel.starredWordsCount)个" : nil
        case .wrongWords:
            return viewModel.wrongWordsCount > 0 ? "\(viewModel.wrongWordsCount)个" : nil
        case .currentArticle:
            // 当前文章分类的进度显示
            if let article = ArticleProgressManager.shared.currentArticle {
                return "\(article.extractedWords.count)个单词"
            }
            return "未选择文章"
        case .junior, .senior, .cet4, .cet6, .postgrad, .toefl:
            // 直接访问ViewModel的可观测属性而不是调用方法
            let progressInfo = viewModel.categoryProgressInfo
            if progressInfo.total > 0 {
                // 优先显示已完成的单词数量，让用户更直观地看到学习成果
                if progressInfo.rounds > 0 {
                    // 如果有完成轮数，显示轮数信息
                    return "\(progressInfo.learned)/\(progressInfo.total) [第\(progressInfo.rounds + 1)轮]"
                } else {
                    // 首轮学习，显示进度
                    return "\(progressInfo.learned)/\(progressInfo.total)"
                }
            }
            return nil
        }
    }
} 
