import SwiftUI

// MARK: - 字符颜色信息
struct CharacterColorInfo {
    let initialColor: Color
    let finalColor: Color
    let component: WordComponent?
}

// MARK: - 飞行字母动画
// MARK: - 字母弹簧掉落动画
struct SpringDropText: View {
    let finalText: String
    let viewModel: RandomWordViewModel
    @State private var displayedCharacters: [AnimatedCharacter] = []
    
    struct AnimatedCharacter: Identifiable {
        let id = UUID()
        let character: Character
        let index: Int
        let colorInfo: CharacterColorInfo
        var isVisible: Bool = false
        var yOffset: CGFloat = -30
        var scale: CGFloat = 0.3
        var colorTransition: CGFloat = 0.0 // 0表示初始颜色，1表示最终颜色
    }
    
    var body: some View {
        HStack(spacing: 2) {
            if displayedCharacters.isEmpty {
                Text("请点击词根或字母")
                    .font(.title)
                    .fontWeight(.bold)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalSecondaryText)
            } else {
                ForEach(displayedCharacters) { charData in
                    Text(String(charData.character))
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .fontDesign(.rounded)
                        .foregroundColor(interpolatedColor(for: charData))
                        .opacity(charData.isVisible ? 1 : 0)
                        .offset(y: charData.yOffset)
                        .scaleEffect(charData.scale)
                }
            }
        }
        .frame(minHeight: 40)
        .frame(maxWidth: .infinity)
        .padding()
        .background(.regularMaterial, in: .rect(cornerRadius: 12))
        .overlay(
            // 添加发光效果
            RoundedRectangle(cornerRadius: 12)
                .stroke(
                    LinearGradient(
                        colors: displayedCharacters.isEmpty ? [.clear] : [Color.animalAccent.opacity(0.3), .clear, Color.animalAccent.opacity(0.3)],
                        startPoint: .leading,
                        endPoint: .trailing
                    ),
                    lineWidth: 2
                )
                .animation(.easeInOut(duration: 2).repeatForever(autoreverses: true), value: displayedCharacters.count)
        )
        .onChange(of: finalText) { oldValue, newValue in
            animateTextChange(from: oldValue, to: newValue)
        }
    }
    
    private func interpolatedColor(for charData: AnimatedCharacter) -> Color {
        let t = charData.colorTransition
        
        // 如果组件颜色和最终颜色相同，直接返回
        if charData.colorInfo.initialColor == charData.colorInfo.finalColor {
            return charData.colorInfo.finalColor
        }
        
        // 使用更安全的颜色插值方法
        if t <= 0 {
            return charData.colorInfo.initialColor
        } else if t >= 1 {
            return charData.colorInfo.finalColor
        } else {
            // 创建一个渐变颜色，使用 t 值在初始颜色和最终颜色之间插值
            return Color(
                red: interpolateComponent(
                    from: charData.colorInfo.initialColor.cgColor?.components?[0] ?? 0,
                    to: charData.colorInfo.finalColor.cgColor?.components?[0] ?? 0,
                    t: t
                ),
                green: interpolateComponent(
                    from: charData.colorInfo.initialColor.cgColor?.components?[1] ?? 0,
                    to: charData.colorInfo.finalColor.cgColor?.components?[1] ?? 0,
                    t: t
                ),
                blue: interpolateComponent(
                    from: charData.colorInfo.initialColor.cgColor?.components?[2] ?? 0,
                    to: charData.colorInfo.finalColor.cgColor?.components?[2] ?? 0,
                    t: t
                )
            )
        }
    }
    
    private func interpolateComponent(from: CGFloat, to: CGFloat, t: CGFloat) -> Double {
        return Double(from * (1 - t) + to * t)
    }
    
    private func getColorInfo(for position: Int) -> CharacterColorInfo {
        let targetComponents = viewModel.getTargetComponents()
        var currentLength = 0
        
        // 找到对应位置的组件
        for component in targetComponents {
            let componentLength = component.text.count
            if position >= currentLength && position < currentLength + componentLength {
                // 找到对应的组件，确定颜色
                let initialColor = getInitialColor(for: component)
                return CharacterColorInfo(
                    initialColor: initialColor,
                    finalColor: Color.animalTextColor,
                    component: component
                )
            }
            currentLength += componentLength
        }
        
        // 没找到组件，使用默认颜色
        return CharacterColorInfo(
            initialColor: Color.animalTextColor,
            finalColor: Color.animalTextColor,
            component: nil
        )
    }
    
    private func getInitialColor(for component: WordComponent) -> Color {
        switch component {
        case .letter:
            return Color.animalButton
        case .morpheme(_, let type):
            switch type {
            case .prefix:
                return Color.animalPrefix
            case .root:
                return Color.animalRoot
            case .suffix:
                return Color.animalSuffix
            }
        case .syllable:
            return Color.animalAccent
        }
    }
    
    private func animateTextChange(from oldText: String, to newText: String) {
        if newText.count > oldText.count && newText.hasPrefix(oldText) {
            // 文字增加且是基于已有文本添加 (包括从空到有) - 只动画显示新增字符
            let newCharacters = String(newText.dropFirst(oldText.count))
            
            // 为新增字符创建动画数据
            for (index, char) in newCharacters.enumerated() {
                let charPosition = oldText.count + index
                let colorInfo = getColorInfo(for: charPosition)
                
                let charData = AnimatedCharacter(
                    character: char,
                    index: displayedCharacters.count + index,
                    colorInfo: colorInfo
                )
                displayedCharacters.append(charData)
                
                // 延迟触发弹簧掉落动画
                let delay = Double(index) * 0.1
                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    withAnimation(.spring(response: 0.5, dampingFraction: 0.5)) {
                        if let charIndex = displayedCharacters.firstIndex(where: { $0.id == charData.id }) {
                            displayedCharacters[charIndex].isVisible = true
                            displayedCharacters[charIndex].yOffset = 0
                            displayedCharacters[charIndex].scale = 1.0
                        }
                    }
                    
                    // 颜色变化动画 - 在掉落动画开始后立即开始，但稍微延迟
                    // 根据动画偏好调整动画时机和持续时间
                    let animationPrefs = AnimationPreferencesManager.shared
                    let config = animationPrefs.getAnimationConfig()
                    let colorDelay = config.enableTransitions ? 0.3 : 0.1
                    let colorDuration = config.duration * 1.5 // 颜色变化时间稍长一些
                    
                    // 如果不是按钮颜色或者在最少动画模式下，跳过颜色变化动画
                    if colorInfo.initialColor != colorInfo.finalColor && config.enableTransitions {
                        DispatchQueue.main.asyncAfter(deadline: .now() + colorDelay) {
                            withAnimation(.easeInOut(duration: colorDuration)) {
                                if let charIndex = displayedCharacters.firstIndex(where: { $0.id == charData.id }) {
                                    displayedCharacters[charIndex].colorTransition = 1.0
                                }
                            }
                        }
                    } else {
                        // 立即设置为最终颜色
                        DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                            if let charIndex = displayedCharacters.firstIndex(where: { $0.id == charData.id }) {
                                displayedCharacters[charIndex].colorTransition = 1.0
                            }
                        }
                    }
                }
            }
        } else {
            // 文字减少、重置或完全改变 - 重新创建所有字符，无动画
            displayedCharacters.removeAll()
            
            if !newText.isEmpty {
                for (index, char) in newText.enumerated() {
                    let colorInfo = getColorInfo(for: index)
                    let charData = AnimatedCharacter(
                        character: char,
                        index: index,
                        colorInfo: colorInfo,
                        isVisible: true,
                        yOffset: 0,
                        scale: 1.0,
                        colorTransition: 1.0 // 重置时直接显示最终颜色
                    )
                    displayedCharacters.append(charData)
                }
            }
        }
    }
}

// MARK: - 按钮弹簧掉落动画
struct ButtonSpringDropAnimation: ViewModifier {
    let delay: Double
    @State private var isVisible = false
    @State private var yOffset: CGFloat = -50
    
    func body(content: Content) -> some View {
        content
            .opacity(isVisible ? 1 : 0)
            .offset(y: yOffset)
            .scaleEffect(isVisible ? 1 : 0.3)
            .onAppear {
                withAnimation(.spring(response: 0.8, dampingFraction: 0.5).delay(delay)) {
                    isVisible = true
                    yOffset = 0
                }
            }
            .onChange(of: isVisible) { _, newValue in
                if !newValue {
                    // 重置动画状态
                    yOffset = -50
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        withAnimation(.spring(response: 0.8, dampingFraction: 0.5)) {
                            isVisible = true
                            yOffset = 0
                        }
                    }
                }
            }
    }
}



struct UserInputView: View {
    let viewModel: RandomWordViewModel

    var body: some View {
        if viewModel.currentWordItem != nil {
            VStack(spacing: 16) {
                // 用户输入显示 - 使用弹簧掉落动画效果
                SpringDropText(finalText: viewModel.userInput, viewModel: viewModel)
                
                // 词根/字母按钮区域
                if viewModel.gameState == .playing {
                    VStack(spacing: 16) {
                        // 词根按钮（流式布局）
                        componentButtonsView
                        .padding(.horizontal)
                    }
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var availableComponents: [(Int, WordComponentButton)] {
        Array(viewModel.shuffledComponents.filter { !$0.isClicked }.enumerated())
    }
    
    private var componentButtonsView: some View {
        FlowLayout(spacing: 10) {
            ForEach(availableComponents, id: \.1.id) { index, componentButton in
                componentButtonView(for: componentButton, at: index)
            }
        }
    }
    
    private func componentButtonView(for componentButton: WordComponentButton, at index: Int) -> some View {
        let onTapAction = {
            // 添加触觉反馈
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
            
            // 调用原有逻辑
            viewModel.componentTapped(componentButton.id)
        }
        
        let animationDelay = Double(index) * 0.05
        
        return WordComponentButtonView(
            componentButton: componentButton,
            onTap: onTapAction
        )
        .modifier(ButtonSpringDropAnimation(delay: animationDelay))
    }
    
}

// MARK: - 辅助视图修饰符
// 已移动到 SharedViewModifiers.swift 
