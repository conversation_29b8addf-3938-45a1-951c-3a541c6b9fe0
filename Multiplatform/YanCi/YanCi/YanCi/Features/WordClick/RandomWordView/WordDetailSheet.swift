import SwiftUI

// MARK: - 单词详情展示 Sheet
struct WordDetailSheet: View {
    let wordDetail: WordDetail
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 20) {
                    // 单词标题
                    wordHeaderView

                    // 例句部分
                    if !wordDetail.quotes.isEmpty {
                        quotesSection
                    }

                    // 词组部分
                    if !wordDetail.phrases.isEmpty {
                        phrasesSection
                    }

                    // 相关词部分
                    if !wordDetail.relatedWords.isEmpty {
                        relatedWordsSection
                    }
                }
                .padding()
            }
            .navigationTitle("单词详情")
            .navigationBarTitleDisplayMode(.inline)
            .background(Color.animalBackgroundColor)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                    .font(.body)
                    .fontWeight(.medium)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalAccent)
                }
            }
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
    }

    // MARK: - 单词标题
    private var wordHeaderView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(wordDetail.word)
                .font(.largeTitle)
                .fontWeight(.bold)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalTextColor)

            Text(wordDetail.translation)
                .font(.title3)
                .fontWeight(.medium)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalAccent)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(.bottom, 10)
    }

    // MARK: - 例句部分
    private var quotesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            sectionHeader(title: "例句", icon: "quote.bubble.fill", color: .blue)

            ForEach(wordDetail.quotes) { quote in
                QuoteCardView(quote: quote)
            }
        }
    }

    // MARK: - 词组部分
    private var phrasesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            sectionHeader(title: "词组", icon: "text.bubble.fill", color: .green)

            ForEach(wordDetail.phrases) { phrase in
                PhraseCardView(phrase: phrase)
            }
        }
    }

    // MARK: - 相关词部分
    private var relatedWordsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            sectionHeader(title: "相关词", icon: "link.circle.fill", color: .orange)

            ForEach(wordDetail.relatedWords) { relatedWord in
                RelatedWordCardView(relatedWord: relatedWord)
            }
        }
    }

    // MARK: - 分组标题
    private func sectionHeader(title: String, icon: String, color: Color) -> some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)

            Text(title)
                .font(.headline)
                .fontWeight(.bold)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalTextColor)

            Spacer()
        }
        .padding(.top, 8)
    }
}

// MARK: - 例句卡片视图
struct QuoteCardView: View {
    let quote: Quote

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 英文例句
            Text(quote.english)
                .font(.body)
                .fontWeight(.medium)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalTextColor)
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 中文翻译
            Text(quote.chinese)
                .font(.subheadline)
                .fontWeight(.regular)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalSecondaryText)
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 来源（如果有）
            if let source = quote.source {
                Text("— \(source)")
                    .font(.caption)
                    .fontWeight(.medium)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalSecondaryText.opacity(0.8))
                    .italic()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.animalCardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - 词组卡片视图
struct PhraseCardView: View {
    let phrase: Phrase

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 词组
            Text(phrase.english)
                .font(.body)
                .fontWeight(.bold)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalAccent)
                .lineLimit(nil)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 含义
            Text(phrase.chinese)
                .font(.body)
                .fontWeight(.medium)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalTextColor)
                .lineLimit(nil)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 例句（如果有）
            if let example = phrase.example {
                Text(example)
                    .font(.subheadline)
                    .fontWeight(.regular)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalSecondaryText)
                    .padding(.top, 4)
                    .lineLimit(nil)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.animalCardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - 相关词卡片视图
struct RelatedWordCardView: View {
    let relatedWord: RelatedWord

    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            // 关系标签
            Text(relatedWord.type)
                .font(.caption)
                .fontWeight(.bold)
                .fontDesign(.rounded)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.orange.gradient)
                )

            VStack(alignment: .leading, spacing: 4) {
                // 相关词
                Text(relatedWord.english)
                    .font(.body)
                    .fontWeight(.bold)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalTextColor)
                    .lineLimit(nil)
                    .frame(maxWidth: .infinity, alignment: .leading)

                // 含义
                Text(relatedWord.chinese)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalSecondaryText)
                    .lineLimit(nil)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }

            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.animalCardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
                )
        )
    }
}

// MARK: - 预览
#Preview {
    WordDetailSheet(
        wordDetail: WordDetail(
            word: "reason",
            translation: "原因，理由",
            quotes: [
                Quote(
                    english: "There's a good reason for everything.",
                    chinese: "每件事都有充分的理由。",
                    source: "经典谚语"
                ),
                Quote(
                    english: "Can you give me one reason why I should believe you?",
                    chinese: "你能给我一个相信你的理由吗？",
                    source: nil
                )
            ],
            phrases: [
                Phrase(
                    english: "for this reason",
                    chinese: "因为这个原因",
                    example: "For this reason, we decided to postpone the meeting."
                ),
                Phrase(
                    english: "stand to reason",
                    chinese: "合情合理",
                    example: "It stands to reason that practice makes perfect."
                )
            ],
            relatedWords: [
                RelatedWord(
                    english: "cause",
                    chinese: "原因，起因",
                    type: "同义词"
                ),
                RelatedWord(
                    english: "excuse",
                    chinese: "借口，理由",
                    type: "近义词"
                )
            ]
        )
    )
}