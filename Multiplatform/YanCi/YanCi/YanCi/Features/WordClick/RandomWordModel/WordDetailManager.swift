import Foundation
import SwiftUI

// MARK: - 单词详情管理器
@Observable
final class WordDetailManager: @unchecked Sendable {
    // MARK: - 状态属性
    private(set) var currentWordDetail: WordDetail?
    private(set) var currentQuote: Quote?
    private(set) var showQuoteAnimation: Bool = false
    private(set) var showDetailSheet: Bool = false
    private(set) var isLoadingDetail: Bool = false
    
    // 服务依赖
    private let wordDetailService = WordDetailService.shared
    private let speechService = SpeechService.shared
    
    // MARK: - 公共方法
    
    /// 单词拼写完成时触发，加载详情并显示例句动画
    @MainActor
    func onWordCompleted(word: String) {
        Task {
            await loadWordDetailAndShowQuote(for: word)
        }
    }
    
    /// 点击例句时显示详情 Sheet
    @MainActor
    func presentDetailSheet() {
        guard currentWordDetail != nil else { return }
        showDetailSheet = true
    }
    
    /// 重置状态，准备下一个单词
    @MainActor
    func reset() {
        currentWordDetail = nil
        currentQuote = nil
        showQuoteAnimation = false
        showDetailSheet = false
        isLoadingDetail = false
    }
    
    /// 手动隐藏例句动画
    @MainActor
    func hideQuoteAnimation() {
        showQuoteAnimation = false
    }
    
    /// 关闭详情 Sheet
    @MainActor
    func dismissDetailSheet() {
        showDetailSheet = false
    }
    
    // MARK: - 私有方法
    
    /// 加载单词详情并显示例句
    @MainActor
    private func loadWordDetailAndShowQuote(for word: String) async {
        isLoadingDetail = true
        
        // 加载单词详情
        currentWordDetail = await wordDetailService.loadWordDetail(for: word)
        
        isLoadingDetail = false
        
        // 获取随机例句并显示动画
        if let wordDetail = currentWordDetail {
            currentQuote = wordDetailService.getRandomQuote(from: wordDetail)

            if let quote = currentQuote {
                // 延迟一点时间再显示动画，让游戏完成效果先播放
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒延迟

                withAnimation(.easeInOut(duration: 0.3)) {
                    showQuoteAnimation = true
                }

                // 播放英文例句读音（不播放中文翻译）
                speechService.speak(quote.english)
            }
        }
    }
} 