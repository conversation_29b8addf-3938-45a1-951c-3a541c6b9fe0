import Foundation
import SwiftData

// MARK: - 字母按钮结构
struct LetterButton {
    let letter: Character
    let id = UUID()
    var isClicked: Bool = false
}

// MARK: - 重构后的 RandomWordViewModel
@Observable
final class RandomWordViewModel: @unchecked Sendable {
    
    // MARK: - 当前状态
    private(set) var currentWordItem: EnWord?
    private(set) var currentCategory: String = ""
    
    // 当前选择的单词分类
    private(set) var selectedCategory: WordCategory = .path {
        didSet {
            UserDefaults.standard.set(selectedCategory.rawValue, forKey: "selectedWordCategory")
        }
    }
    
    // 添加可观测的分类进度信息属性
    private(set) var categoryProgressInfo: (learned: Int, total: Int, rounds: Int) = (0, 0, 0)
    
    // 添加可观测的路径学习进度信息属性
    private(set) var pathProgressInfo: (currentGroup: Int, totalGroups: Int, currentGroupName: String, progress: String, rounds: Int) = (0, 0, "", "", 0)
    
    // 路径学习相关状态
    var showGroupCompleted: Bool = false
    private(set) var completedGroupName: String = ""
    private(set) var completedGroupIndex: Int = 0
    
    // 轮次完成相关状态
    var showRoundCompleted: Bool = false
    private(set) var completedRound: Int = 0
    
    // 分类轮次完成相关状态
    var showCategoryRoundCompleted: Bool = false
    private(set) var completedCategoryRound: Int = 0
    private(set) var completedCategory: WordCategory?
    
    // 统计数据
    private(set) var starredWordsCount: Int = 0
    private(set) var wrongWordsCount: Int = 0
    
    // MARK: - 管理器
    let coinManager = CoinManager.shared
    let petManager = PetManager.shared
    private let gameManager = WordGameManager()
    private let wordDataManager: WordDataManager
    private let decompositionManager = WordDecompositionManager()
    private let collectionManager: WordCollectionManager
    private let rewardManager: GameRewardManager
    let dailyStreakManager = DailyStreakManager.shared
    private let speechService = SpeechService.shared
    let wordDetailManager = WordDetailManager()
    
    // MARK: - 初始化
    
    init(modelContext: ModelContext? = nil) {
        self.wordDataManager = WordDataManager(modelContext: modelContext)
        self.collectionManager = WordCollectionManager(modelContext: modelContext)
        self.rewardManager = GameRewardManager(modelContext: modelContext)
        
        // 从持久化存储中加载分类选择
        if let savedCategoryRawValue = UserDefaults.standard.object(forKey: "selectedWordCategory") as? String,
           let savedCategory = WordCategory(rawValue: savedCategoryRawValue) {
            self.selectedCategory = savedCategory
        }
        
        // 设置路径学习完成回调
        PathLearningManager.shared.onGroupCompleted = { [unowned self] groupIndex, groupName in
            print("🎯 路径学习组完成回调触发: 第\(groupIndex + 1)组 \(groupName)")
            Task { @MainActor in
                // 确保在主线程上更新UI状态
                self.showGroupCompleted = true
                self.completedGroupName = groupName
                self.completedGroupIndex = groupIndex
                
                // 添加调试信息
                print("✅ 路径学习弹窗状态已设置: showGroupCompleted=\(self.showGroupCompleted)")
            }
        }
        
        // 设置轮次完成回调
        PathLearningManager.shared.onRoundCompleted = { [unowned self] completedRounds in
            print("🏆 路径学习轮次完成回调触发: 第\(completedRounds)轮")
            Task { @MainActor in
                self.showRoundCompleted = true
                self.completedRound = completedRounds
                
                // 轮次完成时更新路径进度信息
                self.updatePathProgressInfo()
                
                print("✅ 轮次完成弹窗状态已设置: showRoundCompleted=\(self.showRoundCompleted)")
            }
        }
        
        // 设置分类轮次完成回调
        CategoryProgressManager.shared.onCategoryRoundCompleted = { [unowned self] category, completedRounds in
            Task { @MainActor in
                self.showCategoryRoundCompleted = true
                self.completedCategoryRound = completedRounds
                self.completedCategory = category
                
                // 轮次完成时更新分类进度信息
                if category == self.selectedCategory {
                    self.updateCategoryProgressInfo()
                }
            }
        }
        
        // 监听词库导入完成通知
        NotificationCenter.default.addObserver(
            forName: .wordImportCompleted,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            Task { @MainActor in
                self?.generateRandomWord()
            }
        }
        
        // 初始化分类进度信息
        self.categoryProgressInfo = CategoryProgressManager.shared.getProgressInfo(for: selectedCategory)
        
        // 初始化路径学习进度信息
        self.pathProgressInfo = PathLearningManager.shared.getProgressInfo()
        
        // 如果有 modelContext，立即生成单词
        if modelContext != nil {
            Task { @MainActor in
                generateRandomWord()
            }
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self, name: .wordImportCompleted, object: nil)
    }
    
    // MARK: - 公共方法
    
    @MainActor
    func setModelContext(_ context: ModelContext) {
        wordDataManager.setModelContext(context)
        collectionManager.setModelContext(context)
        rewardManager.setModelContext(context)
        
        updateStarredWordsCount()
        updateWrongWordsCount()
        updateCategoryProgressInfo()
        
        // 初始化时也更新路径进度信息
        updatePathProgressInfo()
        
        if currentWordItem == nil {
            generateRandomWord()
        } else {
            rewardManager.checkWordOwnership(for: currentWordItem)
        }
    }
    
    @MainActor
    func setWordCategory(_ category: WordCategory) {
        selectedCategory = category
        updateCategoryProgressInfo()
        
        // 如果切换到路径学习模式，更新路径进度信息
        if category == .path {
            updatePathProgressInfo()
        }
        
        generateRandomWord()
    }
    
    @MainActor
    func generateRandomWord() {
        // 重置游戏状态
        gameManager.startNewGame()
        rewardManager.resetRewards()
        
        // 重置单词详情状态
        wordDetailManager.reset()
        
        // 获取新单词
        currentWordItem = wordDataManager.getRandomWord(from: selectedCategory)
        
        guard let wordItem = currentWordItem else {
            currentCategory = getEmptyStateMessage()
            return
        }
        
        // 更新分类显示
        updateCategoryDisplay()
        
        // 设置单词拆分
        decompositionManager.setupDecomposition(for: wordItem.word)
        
        // 检查宠物出现
        rewardManager.checkPetAppearance(for: wordItem, selectedCategory: selectedCategory)
        
        // 检查单词所有权
        rewardManager.checkWordOwnership(for: wordItem)
        
        // 播放单词读音
        speechService.speak(wordItem.word)
    }
    
    @MainActor
    func componentTapped(_ buttonId: UUID) {
        guard gameManager.gameState == .playing,
              let wordItem = currentWordItem,
              gameManager.hasEnergy() else {
            if !gameManager.hasEnergy() {
                gameManager.setOutOfEnergy()
            }
            return
        }
        
        guard let clickedComponent = decompositionManager.findUnclickedComponent(with: buttonId) else { return }
        
        // 检查当前应该点击的组件
        if let expectedComponent = decompositionManager.getExpectedComponent(for: gameManager.userInput) {
            if clickedComponent == expectedComponent {
                // 点击正确
                handleCorrectComponentInput(buttonId: buttonId, component: clickedComponent, word: wordItem)
            } else {
                // 点击错误
                handleIncorrectInput(buttonId: buttonId, word: wordItem)
            }
        }
    }
    
    @MainActor
    func letterTapped(_ buttonId: UUID) {
        guard gameManager.gameState == .playing,
              let wordItem = currentWordItem,
              gameManager.hasEnergy() else {
            if !gameManager.hasEnergy() {
                gameManager.setOutOfEnergy()
            }
            return
        }
        
        guard let clickedLetter = decompositionManager.findUnclickedLetter(with: buttonId) else { return }
        
        // 检查当前应该点击的字母
        if let expectedLetter = decompositionManager.getExpectedLetter(for: gameManager.userInput, in: wordItem.word) {
            if clickedLetter == expectedLetter {
                // 点击正确
                handleCorrectLetterInput(buttonId: buttonId, letter: clickedLetter, word: wordItem)
            } else {
                // 点击错误
                handleIncorrectInput(buttonId: buttonId, word: wordItem)
            }
        }
    }
    
    @MainActor
    func nextWord() {
        generateRandomWord()
    }
    
    @MainActor
    func forceRefresh() {
        generateRandomWord()
    }
    
    @MainActor
    func retryAfterEnergyCheck() -> Bool {
        return gameManager.retryAfterEnergyCheck()
    }
    
    @MainActor
    func toggleCurrentWordStar() {
        guard let wordItem = currentWordItem else { return }
        
        do {
            try collectionManager.toggleWordStar(wordItem)
            updateStarredWordsCount()
        } catch {
            print("切换标星状态失败: \(error)")
        }
    }
    
    @MainActor
    func toggleMorphemeInfo() {
        decompositionManager.toggleMorphemeInfo()
    }
    
    @MainActor
    func hideGroupCompletedDialog() {
        showGroupCompleted = false
        completedGroupName = ""
        completedGroupIndex = 0
    }
    
    @MainActor
    func hideRoundCompletedDialog() {
        showRoundCompleted = false
        completedRound = 0
    }
    
    @MainActor
    func hideCategoryRoundCompletedDialog() {
        showCategoryRoundCompleted = false
        completedCategoryRound = 0
        completedCategory = nil
    }
    
    @MainActor
    func resetPathProgress() {
        PathLearningManager.shared.resetProgress()
        generateRandomWord()
    }
    
    // MARK: - 私有方法
    
    @MainActor
    private func handleCorrectComponentInput(buttonId: UUID, component: WordComponent, word: EnWord) {
        gameManager.processCorrectInput(component.text)
        decompositionManager.markComponentClicked(buttonId, isCorrect: true)
        
        // 检查是否完成整个单词
        if gameManager.userInput.lowercased() == word.word.lowercased() {
            completeWord(word: word)
        }
    }
    
    @MainActor
    private func handleCorrectLetterInput(buttonId: UUID, letter: Character, word: EnWord) {
        gameManager.processCorrectInput(String(letter))
        decompositionManager.markLetterClicked(buttonId)
        
        // 检查是否完成整个单词
        if gameManager.userInput.count == word.word.count {
            completeWord(word: word)
        }
    }
    
    @MainActor
    private func handleIncorrectInput(buttonId: UUID, word: EnWord) {
        gameManager.processIncorrectInput(expectedWord: word.word)
        decompositionManager.markComponentClicked(buttonId, isCorrect: false)

        if gameManager.gameState != .outOfEnergy {
            // 自动完成单词拼写
            gameManager.autoCompleteWord(word.word)
            decompositionManager.markAllComponentsClicked()

            // 处理错词本逻辑
            collectionManager.handleWordError(
                word,
                gameManager: gameManager,
                isInWrongWordsMode: selectedCategory == .wrongWords
            )

            // 立即更新错词本数量
            updateWrongWordsCount()

            // 无论正确与否都显示例句和单词详情
            wordDetailManager.onWordCompleted(word: word.word)
        }
    }
    
    @MainActor
    private func completeWord(word: EnWord) {
        gameManager.completeWord()
        
        // 处理奖励
        rewardManager.handleWordCompletion(
            word: word,
            hasError: gameManager.hasErrorInCurrentWord,
            wordCollectionManager: collectionManager,
            gameManager: gameManager,
            selectedCategory: selectedCategory
        )
        
        // 标记单词为已学习
        if selectedCategory == .path {
            // 路径学习模式：通知 PathLearningManager 标记单词为已完成
            PathLearningManager.shared.markCurrentGroupWordAsCompleted(word.word)
        } else if CategoryProgressManager.shared.isProgressSupported(for: selectedCategory) {
            // 其他支持进度管理的分类
            wordDataManager.markWordAsLearned(word.word, for: selectedCategory)
        }
        
        // 触发单词详情和例句动画
        wordDetailManager.onWordCompleted(word: word.word)
        
        // 确保数据写入完成后立即更新UI
        DispatchQueue.main.async {
            self.updateCategoryProgressInfo()
            self.updateWrongWordsCount()
            self.updateCategoryDisplay()
            
            // 如果是路径学习模式，同时更新路径进度信息
            if self.selectedCategory == .path {
                self.updatePathProgressInfo()
            }
        }
    }
    
    @MainActor
    private func updateCategoryDisplay() {
        currentCategory = selectedCategory.displayName
    }
    
    private func getEmptyStateMessage() -> String {
        switch selectedCategory {
        case .path:
            return "路径学习已完成"
        case .starred:
            return selectedCategory.displayName
        case .wrongWords:
            return selectedCategory.displayName
        default:
            return selectedCategory.displayName
        }
    }
    
    @MainActor
    private func updateStarredWordsCount() {
        starredWordsCount = wordDataManager.getStarredWordsCount()
    }
    
    @MainActor
    private func updateWrongWordsCount() {
        wrongWordsCount = wordDataManager.getWrongWordsCount()
    }
    
    @MainActor
    private func updateCategoryProgressInfo() {
        categoryProgressInfo = CategoryProgressManager.shared.getProgressInfo(for: selectedCategory)
    }
    
    @MainActor
    private func updatePathProgressInfo() {
        pathProgressInfo = PathLearningManager.shared.getProgressInfo()
    }
    
    // MARK: - 访问器属性
    
    var gameState: WordGameManager.GameState { gameManager.gameState }
    var userInput: String { gameManager.userInput }
    var hasErrorInCurrentWord: Bool { gameManager.hasErrorInCurrentWord }
    var errorMessage: String { gameManager.errorMessage }
    var showError: Bool { gameManager.showError }
    
    var shuffledLetters: [LetterButton] { decompositionManager.getShuffledLetters() }
    var shuffledComponents: [WordComponentButton] { decompositionManager.getShuffledComponents() }
    var showMorphemeInfo: Bool { decompositionManager.showMorphemeInfo }
    
    var showPet: Bool { rewardManager.showPet }
    var gotNewPet: Bool { rewardManager.gotNewPet }
    var isWordOwned: Bool { rewardManager.isWordOwned }
    var gainedExp: Bool { rewardManager.gainedExp }
    
    // MARK: - 便利方法
    
    func getTargetComponents() -> [WordComponent] {
        return decompositionManager.getTargetComponents()
    }
    
    func getCurrentWordExp() -> Int {
        return rewardManager.getCurrentWordExp(for: currentWordItem)
    }
    
    func getCurrentWordGroupStatus() -> String? {
        guard selectedCategory == .path, let wordItem = currentWordItem else { return nil }
        let pathManager = PathLearningManager.shared
        return pathManager.isWordInCurrentGroup(wordItem.word) ? "当前组" : "已学组"
    }
    
    func getOwnedPetsCount() -> Int {
        return rewardManager.getOwnedPetsCount()
    }
    
    func getStarredWordsCount() -> Int {
        return starredWordsCount
    }
    
    func getWrongWordsCount() -> Int {
        return wrongWordsCount
    }
    
    func getPathProgressInfo() -> (currentGroup: Int, totalGroups: Int, currentGroupName: String, progress: String, rounds: Int) {
        return pathProgressInfo
    }
    
    // 获取分类进度信息 - 现在直接返回缓存的属性值
    func getCategoryProgressInfo() -> (learned: Int, total: Int, rounds: Int) {
        return categoryProgressInfo
    }
    
    // 强制刷新UI - 新增方法
    @MainActor 
    func forceUIRefresh() {
        // 通过更新currentCategory来触发UI刷新
        updateCategoryDisplay()
    }
    
    @MainActor
    func restoreEnergyForTesting() {
        EnergyManager.shared.restoreEnergyForTesting()
    }
    
    // MARK: - 能量管理代理方法
    
    @MainActor
    func getEnergyInfo() -> (current: Int, max: Int) {
        return EnergyManager.shared.getEnergyInfo()
    }
    
    func getTimeUntilReset() -> String {
        return EnergyManager.shared.getTimeUntilReset()
    }
    
    // MARK: - 语音管理代理方法
    
    @MainActor
    func isSpeechEnabled() -> Bool {
        return speechService.getEnabled()
    }
    
    @MainActor
    func toggleSpeechEnabled() {
        speechService.setEnabled(!speechService.getEnabled())
    }
    
    @MainActor
    func speakCurrentWord() {
        guard let wordItem = currentWordItem else { return }
        speechService.speak(wordItem.word)
    }
    
    // MARK: - 标星管理代理方法
    
    @MainActor
    func toggleStar() {
        guard let wordItem = currentWordItem else { return }
        
        do {
            try collectionManager.toggleWordStar(wordItem)
            updateStarredWordsCount()
        } catch {
            print("切换标星状态失败: \(error)")
        }
    }
    
    // MARK: - 游戏操作代理方法
    
    @MainActor
    func resetCurrentWord() {
        // 重新开始当前单词
        generateRandomWord()
    }
    
    @MainActor
    func showHint() {
        // 显示提示 - 暂时切换到词根信息显示
        decompositionManager.toggleMorphemeInfo()
        
        // 3秒后自动关闭提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            if self.decompositionManager.showMorphemeInfo {
                self.decompositionManager.toggleMorphemeInfo()
            }
        }
    }

} 
