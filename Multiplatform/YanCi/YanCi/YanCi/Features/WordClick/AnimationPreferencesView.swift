import SwiftUI

// MARK: - 动画偏好设置界面
struct AnimationPreferencesView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var animationManager = AnimationPreferencesManager.shared
    @State private var showDeviceInfo = false
    @State private var showResetAlert = false
    @State private var showPerformanceTip = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 顶部说明
                    HeaderSectionView()
                    
                    // 动画强度设置
                    AnimationIntensitySection()
                    
                    // 功能开关
                    FeatureToggleSection()
                    
                    // 性能监控
                    PerformanceSection()
                    
                    // 设备信息
                    DeviceInfoSection()
                    
                    // 重置选项
                    ResetSection()
                }
                .padding()
            }
            .background(Color.animalBackgroundColor)
            .navigationTitle("动画设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                    .font(.body)
                    .fontWeight(.bold)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalAccent)
                }
            }
        }
        .presentationDetents([.large])
        .presentationDragIndicator(.visible)
        .onAppear {
            checkPerformanceTip()
        }
        .alert("性能建议", isPresented: $showPerformanceTip) {
            Button("好的") { }
            Button("查看设备信息") {
                showDeviceInfo = true
            }
        } message: {
            if let suggestion = animationManager.getPerformanceSuggestion() {
                Text(suggestion)
            }
        }
        .alert("重置设置", isPresented: $showResetAlert) {
            Button("取消", role: .cancel) { }
            Button("重置", role: .destructive) {
                resetAllSettings()
            }
        } message: {
            Text("将重置所有动画和手势设置到默认状态，此操作不可恢复。")
        }
        .sheet(isPresented: $showDeviceInfo) {
            DeviceInfoDetailView()
        }
    }
    
    // MARK: - 检查性能提示
    private func checkPerformanceTip() {
        if animationManager.getPerformanceSuggestion() != nil {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                showPerformanceTip = true
            }
        }
    }
    
    private func resetAllSettings() {
        withAnimation(.spring()) {
            animationManager.resetLearningBehavior()
            animationManager.forceUpdateConfig()
        }
    }
}

// MARK: - 头部说明区域
struct HeaderSectionView: View {
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "sparkles")
                    .font(.title2)
                    .foregroundColor(Color.animalAccent)
                
                Text("个性化动画体验")
                    .font(.title2)
                    .fontWeight(.bold)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalTextColor)
                
                Spacer()
            }
            
            Text("根据您的设备性能和使用习惯，智能调节动画效果，提供最佳的学习体验。")
                .font(.body)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalSecondaryText)
                .multilineTextAlignment(.leading)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(Color.animalCardBackground, in: .rect(cornerRadius: 16))
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - 动画强度设置区域
struct AnimationIntensitySection: View {
    @State private var animationManager = AnimationPreferencesManager.shared
    
    var body: some View {
        VStack(spacing: 16) {
            SectionHeaderView(
                title: "动画强度",
                icon: "wand.and.stars",
                description: "选择适合您的动画效果级别"
            )
            
            VStack(spacing: 12) {
                ForEach(AnimationPreferencesManager.AnimationIntensity.allCases, id: \.rawValue) { intensity in
                    IntensityOptionView(
                        intensity: intensity,
                        isSelected: animationManager.userPreferredIntensity == intensity,
                        onSelect: {
                            withAnimation(animationManager.getSpringAnimation(duration: 0.4)) {
                                animationManager.userPreferredIntensity = intensity
                            }
                        }
                    )
                }
            }
        }
        .padding()
        .background(Color.animalCardBackground, in: .rect(cornerRadius: 16))
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - 强度选项视图
struct IntensityOptionView: View {
    let intensity: AnimationPreferencesManager.AnimationIntensity
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 16) {
                // 选择指示器
                ZStack {
                    Circle()
                        .fill(isSelected ? Color.animalAccent : Color.animalSecondaryText.opacity(0.3))
                        .frame(width: 20, height: 20)
                    
                    if isSelected {
                        Circle()
                            .fill(.white)
                            .frame(width: 8, height: 8)
                            .scaleEffect(isSelected ? 1.0 : 0.0)
                            .animation(.spring(response: 0.3), value: isSelected)
                    }
                }
                
                // 内容区域
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(intensity.displayName)
                            .font(.headline)
                            .fontWeight(.bold)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalTextColor)
                        
                        Spacer()
                        
                        if intensity == .auto {
                            Text("推荐")
                                .font(.caption2)
                                .fontWeight(.bold)
                                .fontDesign(.rounded)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color.animalAccent, in: .rect(cornerRadius: 8))
                        }
                    }
                    
                    Text(intensity.description)
                        .font(.body)
                        .fontDesign(.rounded)
                        .foregroundColor(Color.animalSecondaryText)
                        .multilineTextAlignment(.leading)
                }
                
                Spacer()
            }
            .padding()
            .background(
                isSelected ? Color.animalAccent.opacity(0.1) : Color.clear,
                in: .rect(cornerRadius: 12)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        isSelected ? Color.animalAccent : Color.animalBorder.opacity(0.3),
                        lineWidth: isSelected ? 2 : 1
                    )
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 功能开关区域
struct FeatureToggleSection: View {
    @State private var animationManager = AnimationPreferencesManager.shared
    
    var body: some View {
        VStack(spacing: 16) {
            SectionHeaderView(
                title: "功能开关",
                icon: "switch.2",
                description: "自定义您的交互体验"
            )
            
            VStack(spacing: 8) {
                ToggleRow(
                    title: "触觉反馈",
                    description: "按钮点击和手势操作的震动反馈",
                    icon: "iphone.radiowaves.left.and.right",
                    isOn: Binding(
                        get: { animationManager.enableHapticFeedback },
                        set: { animationManager.enableHapticFeedback = $0 }
                    )
                )
                
                ToggleRow(
                    title: "音效反馈",
                    description: "操作成功时的声音提示",
                    icon: "speaker.wave.2",
                    isOn: Binding(
                        get: { animationManager.enableSoundEffects },
                        set: { animationManager.enableSoundEffects = $0 }
                    )
                )
                
                ToggleRow(
                    title: "智能建议",
                    description: "根据性能自动调节动画设置",
                    icon: "brain.head.profile",
                    isOn: Binding(
                        get: { animationManager.enableSmartSuggestions },
                        set: { animationManager.enableSmartSuggestions = $0 }
                    )
                )
            }
        }
        .padding()
        .background(Color.animalCardBackground, in: .rect(cornerRadius: 16))
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
        )
    }
}



// MARK: - 性能监控区域
struct PerformanceSection: View {
    @State private var animationManager = AnimationPreferencesManager.shared
    
    var body: some View {
        VStack(spacing: 16) {
            SectionHeaderView(
                title: "性能监控",
                icon: "speedometer",
                description: "实时监控应用性能表现"
            )
            
            VStack(spacing: 12) {
                PerformanceIndicator(
                    title: "当前强度",
                    value: animationManager.currentIntensity.displayName,
                    color: intensityColor(animationManager.currentIntensity)
                )
                
                PerformanceIndicator(
                    title: "性能问题",
                    value: "\(animationManager.learningBehavior.performanceIssues) 次",
                    color: animationManager.learningBehavior.performanceIssues > 3 ? .red : .green
                )
                
                if let suggestion = animationManager.getPerformanceSuggestion() {
                    HStack(spacing: 8) {
                        Image(systemName: "lightbulb")
                            .foregroundColor(.orange)
                        
                        Text(suggestion)
                            .font(.caption)
                            .fontDesign(.rounded)
                            .foregroundColor(Color.animalSecondaryText)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(.orange.opacity(0.1), in: .rect(cornerRadius: 8))
                }
            }
        }
        .padding()
        .background(Color.animalCardBackground, in: .rect(cornerRadius: 16))
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
        )
    }
    
    private func intensityColor(_ intensity: AnimationPreferencesManager.AnimationIntensity) -> Color {
        switch intensity {
        case .minimal: return .orange
        case .moderate: return .blue
        case .full: return .green
        case .auto: return .purple
        }
    }
}

// MARK: - 性能指标显示
struct PerformanceIndicator: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.body)
                .fontDesign(.rounded)
                .foregroundColor(Color.animalTextColor)
            
            Spacer()
            
            Text(value)
                .font(.body)
                .fontWeight(.bold)
                .fontDesign(.rounded)
                .foregroundColor(color)
        }
    }
}

// MARK: - 设备信息区域
struct DeviceInfoSection: View {
    @State private var showDeviceInfo = false
    
    var body: some View {
        VStack(spacing: 12) {
            Button(action: {
                showDeviceInfo = true
            }) {
                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(Color.animalAccent)
                    
                    Text("查看设备信息")
                        .font(.body)
                        .fontWeight(.medium)
                        .fontDesign(.rounded)
                        .foregroundColor(Color.animalAccent)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(Color.animalSecondaryText)
                }
                .padding()
                .background(Color.animalCardBackground, in: .rect(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.animalBorder.opacity(0.3), lineWidth: 1)
                )
            }
            .buttonStyle(.plain)
        }
        .sheet(isPresented: $showDeviceInfo) {
            DeviceInfoDetailView()
        }
    }
}

// MARK: - 重置区域
struct ResetSection: View {
    @Binding var showResetAlert: Bool
    
    init(showResetAlert: Binding<Bool> = .constant(false)) {
        self._showResetAlert = showResetAlert
    }
    
    var body: some View {
        VStack(spacing: 12) {
            Button(action: {
                showResetAlert = true
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(.red)
                    
                    Text("重置所有设置")
                        .font(.body)
                        .fontWeight(.medium)
                        .fontDesign(.rounded)
                        .foregroundColor(.red)
                    
                    Spacer()
                }
                .padding()
                .background(Color.animalCardBackground, in: .rect(cornerRadius: 12))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                )
            }
            .buttonStyle(.plain)
        }
    }
}

// MARK: - 设备详细信息视图
struct DeviceInfoDetailView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var animationManager = AnimationPreferencesManager.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // 设备硬件信息
                    VStack(alignment: .leading, spacing: 12) {
                        Text("设备信息")
                            .font(.headline)
                            .fontWeight(.bold)
                            .fontDesign(.rounded)
                        
                        Text(animationManager.getDeviceInfo())
                            .font(.body)
                            .fontDesign(.monospaced)
                            .foregroundColor(Color.animalSecondaryText)
                    }
                    .padding()
                    .background(Color.animalCardBackground, in: .rect(cornerRadius: 12))
                }
                .padding()
            }
            .background(Color.animalBackgroundColor)
            .navigationTitle("设备详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
        .presentationDetents([.medium, .large])
    }
}

// MARK: - 通用组件

struct SectionHeaderView: View {
    let title: String
    let icon: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(Color.animalAccent)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.bold)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalTextColor)
                
                Text(description)
                    .font(.caption)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalSecondaryText)
            }
            
            Spacer()
        }
    }
}

struct ToggleRow: View {
    let title: String
    let description: String
    let icon: String
    @Binding var isOn: Bool
    
    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(Color.animalAccent)
                .frame(width: 24)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.body)
                    .fontWeight(.medium)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalTextColor)
                
                Text(description)
                    .font(.caption)
                    .fontDesign(.rounded)
                    .foregroundColor(Color.animalSecondaryText)
            }
            
            Spacer()
            
            Toggle("", isOn: $isOn)
                .tint(Color.animalAccent)
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    AnimationPreferencesView()
} 