import SwiftUI
import Observation
import Foundation
import SwiftData

// MARK: - 单词翻页转场效果
struct WordFlipTransition: ViewModifier {
    let isChanging: Bool
    let shouldAnimate: Bool  // 新增：是否应该执行动画
    @State private var rotationY: Double = 0
    @State private var scale: CGFloat = 1.0
    @State private var animationManager = AnimationPreferencesManager.shared

    func body(content: Content) -> some View {
        content
            .rotation3DEffect(
                .degrees(rotationY),
                axis: (x: 0, y: 1, z: 0),
                anchor: .center,
                perspective: 0.5
            )
            .scaleEffect(scale)
            .onChange(of: isChanging) { _, newValue in
                if newValue && shouldAnimate && animationManager.shouldShowTransitions() {
                    performFlipAnimation()
                }
            }
    }

    private func performFlipAnimation() {
        let config = animationManager.getAnimationConfig()
        let duration = config.duration * 0.6 // 稍微快一些的翻转效果

        // 第一阶段：翻转到90度
        withAnimation(animationManager.getEaseAnimation(duration: duration)) {
            rotationY = 90
            scale = 0.8
        }

        // 第二阶段：翻转到0度
        DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
            rotationY = -90
            withAnimation(animationManager.getEaseAnimation(duration: duration)) {
                rotationY = 0
                scale = 1.0
            }
        }
    }
}



// MARK: - 主游戏区域视图
struct MainGameAreaView: View {
    @State var viewModel: RandomWordViewModel
    @Binding var showCategorySelection: Bool
    @Binding var isWordChanging: Bool
    let hasInitialized: Bool
    
    var body: some View {
        ZStack {
            if viewModel.currentWordItem != nil {
                gameContentView
            } else {
                emptyStateView
            }
        }
        .frame(maxHeight: .infinity)
    }
    
    private var gameContentView: some View {
        VStack(spacing: 20) {
            WordDisplayView(viewModel: viewModel)
                .modifier(WordFlipTransition(isChanging: hasInitialized ? isWordChanging : false, shouldAnimate: hasInitialized))
            
            UserInputView(viewModel: viewModel)
            
            // 游戏状态卡片 - 移动到单词展示下方
            if viewModel.gameState != .playing {
                GameStateView(
                    viewModel: viewModel,
                    showCategorySelection: $showCategorySelection
                )
            }
            
            WordTranlateView(viewModel: viewModel)
        }
    }
    
    private var emptyStateView: some View {
        GameStateView(
            viewModel: viewModel,
            showCategorySelection: $showCategorySelection
        )
    }
}

// MARK: - 主视图内容
struct MainContentView: View {
    @State var viewModel: RandomWordViewModel
    @Binding var showStreakView: Bool
    @Binding var showPetListView: Bool
    @Binding var showCategorySelection: Bool
    @Binding var showStarredWords: Bool
    @Binding var showWrongWords: Bool
    @Binding var showPathProgress: Bool
    @Binding var showCategoryProgress: Bool
    @Binding var showSettings: Bool
    @Binding var showArticleManager: Bool
    @Binding var isWordChanging: Bool
    let hasInitialized: Bool
    
    var body: some View {
        VStack(spacing: 10) {
            // 顶部状态栏
            topStatusBar
            
            Spacer()
            
            // 主游戏区域
            MainGameAreaView(
                viewModel: viewModel,
                showCategorySelection: $showCategorySelection,
                isWordChanging: $isWordChanging,
                hasInitialized: hasInitialized
            )
            
            Spacer()
            
            // 底部控制栏
            bottomControlBar
        }
        .padding()
    }
    
    private var topStatusBar: some View {
        RandomWordFooterUpView(
            viewModel: viewModel,
            showPetListView: $showPetListView,
            showStarredWords: $showStarredWords,
            showWrongWords: $showWrongWords,
            showSettings: $showSettings,
            showArticleManager: $showArticleManager
        )
    }
    
    private var bottomControlBar: some View {
        RandomWordFooterDownView(
            viewModel: viewModel,
            showStreakView: $showStreakView,
            showCategorySelection: $showCategorySelection,
            showPathProgress: $showPathProgress,
            showCategoryProgress: $showCategoryProgress
        )
    }
}

// MARK: - 文章阅读覆层视图
struct ArticleOverlayView: View {
    @Binding var showArticleOverlay: Bool
    @Binding var selectedArticle: Article?
    @State var viewModel: RandomWordViewModel
    @State private var articleProgressManager = ArticleProgressManager.shared
    
    var body: some View {
        Group {
            if showArticleOverlay, let article = selectedArticle {
                FullScreenArticleOverlay(
                    article: article,
                    onStartSpelling: {
                        // 设置当前文章并开始拼写
                        articleProgressManager.setCurrentArticle(article)
                        showArticleOverlay = false
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                            viewModel.setWordCategory(.currentArticle)
                        }
                    },
                    onDismiss: {
                        showArticleOverlay = false
                        selectedArticle = nil
                    }
                )
            }
        }
    }
}

struct RandomWordView: View {
    @State private var viewModel = RandomWordViewModel()
    @State private var showStreakView = false
    @State private var showPetListView = false
    @State private var showCategorySelection = false
    @State private var showStarredWords = false
    @State private var showWrongWords = false
    @State private var showPathProgress = false
    @State private var showCategoryProgress = false
    @State private var showSettings = false
    @State private var showAnimationPreferences = false
    @State private var showArticleManager = false
    @State private var showArticleOverlay = false
    @State private var selectedArticle: Article?
    @State private var isWordChanging = false
    @State private var animationManager = AnimationPreferencesManager.shared
    @State private var hasInitialized = false  // 新增：初始化完成标志
    @Environment(\.modelContext) private var modelContext

    var body: some View {
        mainView
            // 优先显示路径学习相关的alert，确保不被sheet遮挡
            .alert("🎉 恭喜完成！", isPresented: $viewModel.showGroupCompleted) {
                Button("继续学习") {
                    viewModel.hideGroupCompletedDialog()
                    // 延迟生成新单词，避免动画冲突
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        viewModel.generateRandomWord()
                    }
                }
                .keyboardShortcut(.defaultAction)
            } message: {
                Text("已完成第 \(viewModel.completedGroupIndex + 1) 组：\(viewModel.completedGroupName)\n\n开始学习下一组单词吧！")
            }
            .alert("🏆 完成一轮学习！", isPresented: $viewModel.showRoundCompleted) {
                Button("开始新一轮") {
                    viewModel.hideRoundCompletedDialog()
                    // 延迟生成新单词，避免动画冲突
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        viewModel.generateRandomWord()
                    }
                }
                .keyboardShortcut(.defaultAction)
            } message: {
                Text("恭喜完成第 \(viewModel.completedRound) 轮路径学习！\n\n已解锁所有 58 个主题组，现在开始新的一轮挑战！")
            }
            .alert("🌟 分类学习完成！", isPresented: $viewModel.showCategoryRoundCompleted) {
                Button("继续学习") {
                    viewModel.hideCategoryRoundCompletedDialog()
                    // 延迟生成新单词，避免动画冲突
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        viewModel.generateRandomWord()
                    }
                }
                .keyboardShortcut(.defaultAction)
            } message: {
                if let category = viewModel.completedCategory {
                    Text("恭喜完成【\(category.rawDisplayName)】第 \(viewModel.completedCategoryRound) 轮学习！")
                } else {
                    Text("恭喜完成一轮学习！")
                }
            }
            // sheet显示在alert之后，确保不会遮挡弹窗
            .sheet(isPresented: $showStreakView) {
                DailyStreakView()
                    .presentationDetents([.medium, .large])
                    .presentationDragIndicator(.visible)
            }
            .sheet(isPresented: $showPetListView) {
                PetListView()
                    .presentationDetents([.large])
                    .presentationDragIndicator(.visible)
            }
            .sheet(isPresented: $showCategorySelection) {
                categorySelectionSheet
            }
            .sheet(isPresented: $showStarredWords) {
                StarredWordsView()
            }
            .sheet(isPresented: $showWrongWords) {
                WrongWordsView()
            }
            .sheet(isPresented: $showPathProgress) {
                PathProgressView()
            }
            .sheet(isPresented: $showCategoryProgress) {
                CategoryProgressView(initialCategory: viewModel.selectedCategory)
            }
            .sheet(isPresented: $showSettings) {
                SettingsView()
            }
            .sheet(isPresented: $showAnimationPreferences) {
                AnimationPreferencesView()
                    .presentationDetents([.medium])
            }
            .sheet(isPresented: $showArticleManager) {
                UnifiedArticleManagerView { article in
                    selectedArticle = article
                    showArticleManager = false
                    showArticleOverlay = true
                }
                .presentationDetents([.large])
                .presentationDragIndicator(.visible)
            }
    }
    
    private var mainView: some View {
        Group {
            ZStack {
                MainContentView(
                    viewModel: viewModel,
                    showStreakView: $showStreakView,
                    showPetListView: $showPetListView,
                    showCategorySelection: $showCategorySelection,
                    showStarredWords: $showStarredWords,
                    showWrongWords: $showWrongWords,
                    showPathProgress: $showPathProgress,
                    showCategoryProgress: $showCategoryProgress,
                    showSettings: $showSettings,
                    showArticleManager: $showArticleManager,
                    isWordChanging: $isWordChanging,
                    hasInitialized: hasInitialized
                )
                .background(backgroundGradient.ignoresSafeArea())
                .overlay(
                    ArticleOverlayView(
                        showArticleOverlay: $showArticleOverlay,
                        selectedArticle: $selectedArticle,
                        viewModel: viewModel
                    )
                )
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        .animation(hasInitialized ? .spring(response: 0.5, dampingFraction: 0.8) : nil, value: viewModel.gameState)
        .animation(hasInitialized ? .spring(response: 0.3, dampingFraction: 0.7) : nil, value: viewModel.showError)
        .animation(hasInitialized ? .spring(response: 0.4, dampingFraction: 0.8) : nil, value: viewModel.currentWordItem?.word)
        .onAppear {
            setupInitialState()
        }
        .onDisappear {
            SpeechService.shared.stopSpeaking()
        }
        .onChange(of: viewModel.currentWordItem?.word) { oldValue, newValue in
            handleWordChange(oldValue: oldValue, newValue: newValue)
        }
        .onChange(of: viewModel.gameState) { oldValue, newValue in
            handleGameStateChange(oldValue: oldValue, newValue: newValue)
        }
    }
    
    private var backgroundGradient: some View {
        LinearGradient(
            colors: [
                Color.animalBackgroundColor,
                Color.animalBackgroundColor.opacity(0.95),
                Color.animalCardBackground.opacity(0.3)
            ],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
    
    private var categorySelectionSheet: some View {
        CategorySelectionView(
            selectedCategory: viewModel.selectedCategory,
            onCategorySelected: { category in
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    viewModel.setWordCategory(category)
                    showCategorySelection = false
                }
            }
        )
        .presentationDetents([.large])
        .presentationDragIndicator(.visible)
    }
    
    // MARK: - 私有方法
    
    private func setupInitialState() {
        // 1. 优先设置管理器的上下文，特别是需要恢复状态的管理器
        ArticleProgressManager.shared.setModelContext(modelContext) // 确保文章状态先被恢复
        
        // 2. 然后再设置主视图模型的上下文，此时它才能获取到已恢复的状态
        viewModel.setModelContext(modelContext)
        
        // 延迟启用动画，确保初始数据加载完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            hasInitialized = true
        }
    }
    
    private func handleWordChange(oldValue: String?, newValue: String?) {
        // 只有在初始化完成后才触发单词切换动画
        if hasInitialized && oldValue != newValue && newValue != nil && oldValue != nil {
            triggerWordChangeAnimation()
        }
    }
    
    private func handleGameStateChange(oldValue: WordGameManager.GameState, newValue: WordGameManager.GameState) {
        if hasInitialized && newValue == .correct && !viewModel.hasErrorInCurrentWord {
            triggerSuccessEffects()
        }
    }
    
    private func triggerWordChangeAnimation() {
        isWordChanging = true

        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            isWordChanging = false
        }
    }
    
    private func triggerSuccessEffects() {
        // 使用智能触觉反馈
        if animationManager.enableHapticFeedback {
            HapticManager.shared.trigger(.wordComplete)
        }
    }
}
